name: 构建并部署到 Netlify

on:
  push:
    branches:
      - 'main'
  workflow_dispatch:

env:
  STORE_PATH: ''

concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: true

jobs:
  # TODO: 这东西太难搞了，之后修吧
  # windows-build:
  #   name: Windows 构建
  #   runs-on: windows-latest
  #   steps:
  #     - name: 签出代码
  #       uses: actions/checkout@v3
  #       with:
  #         fetch-depth: 0

  #     - name: 安装 Node.js 20.x
  #       uses: actions/setup-node@v3
  #       with:
  #         node-version: 20.x

  #     - name: 安装 pnpm
  #       uses: pnpm/action-setup@v2
  #       with:
  #         run_install: false
  #         version: 8

  #     - name: 获取 pnpm store 目录
  #       shell: bash
  #       run: |
  #         echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

  #     - name: 配置 pnpm 缓存
  #       uses: actions/cache@v3
  #       with:
  #         path: ${{ env.STORE_PATH }}
  #         key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
  #         restore-keys: |
  #           ${{ runner.os }}-pnpm-store-

  #     - name: 安装依赖
  #       run: pnpm install --frozen-lockfile

  #     - name: 安装思源黑体
  #       run: |
  #         mkdir -p ~/.local/share/fonts
  #         cp public/source-han-sans.ttf ~/.local/share/fonts/source-han-sans.ttf

  #     - name: 构建
  #       run: pnpm docs:build
  build:
    name: Ubuntu 构建和推送
    runs-on: ubuntu-latest
    environment:
      name: 正式 Production
      url: https://nolebase.ayaka.io
    steps:
      - name: 签出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 安装 pnpm
        uses: pnpm/action-setup@v4
        with:
          run_install: false

      - name: 安装 Node.js 23.x
        uses: actions/setup-node@v4
        with:
          node-version: 23.x
          cache: 'pnpm'

      - name: 安装依赖
        run: pnpm install --frozen-lockfile

      - name: 安装思源黑体
        run: |
          mkdir -p ~/.local/share/fonts
          cp public/source-han-sans.ttf ~/.local/share/fonts/source-han-sans.ttf

      - name: 构建
        run: pnpm docs:build

      - name: 安装 Netlify CLI
        run: pnpm install -g netlify-cli

      - name: 推送到 Netlify
        timeout-minutes: 10
        run: netlify deploy --dir .vitepress/dist --prod
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NETLIFY_SITE_ID: ${{ secrets.NETLIFY_SITE_ID }}

