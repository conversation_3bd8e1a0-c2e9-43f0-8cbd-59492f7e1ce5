{"main": {"id": "25f4cc8f077230c0", "type": "split", "children": [{"id": "d60d9813f66a1cce", "type": "tabs", "children": [{"id": "8b8974485c5430a1", "type": "leaf", "state": {"type": "markdown", "state": {"file": "zh-CN/笔记/🛠️ 开发/为 DuckDB WASM 制作 Drizzle ORM Driver.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "为 DuckDB WASM 制作 Drizzle ORM Driver"}}, {"id": "ea65bc0da501beda", "type": "leaf", "state": {"type": "markdown", "state": {"file": "zh-CN/笔记/🛠️ 开发/为 DuckDB WASM 制作 Drizzle ORM Driver.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "为 DuckDB WASM 制作 Drizzle ORM Driver"}}], "currentTab": 1}], "direction": "vertical"}, "left": {"id": "3698b8c616aabcfc", "type": "split", "children": [{"id": "b15e2c7929b2a46a", "type": "tabs", "children": [{"id": "b5dfc4e6c8287ee4", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical"}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "d5332c2bfa4468de", "type": "leaf", "state": {"type": "search", "state": {"query": "基础设施 AWS", "matchingCase": false, "explainSearch": false, "collapseAll": true, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "f568bf7e439d398b", "type": "leaf", "state": {"type": "starred", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "955041b03cbffb02", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}, {"id": "0972447421cfef3d", "type": "leaf", "state": {"type": "recent-files", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}]}], "direction": "horizontal", "width": 340.5}, "right": {"id": "246bd0838f8f40a6", "type": "split", "children": [{"id": "166ecf9eee3475b0", "type": "tabs", "dimension": 31.92019950124688, "children": [{"id": "d9c447a5c56d10b5", "type": "leaf", "state": {"type": "localgraph", "state": {"file": "笔记/🤖 AI 人工智能/配置 CUDA 的一万种方法.md", "options": {"collapse-filter": false, "search": "", "localJumps": 1, "localBacklinks": true, "localForelinks": true, "localInterlinks": true, "showTags": true, "showAttachments": false, "hideUnresolved": false, "collapse-color-groups": false, "colorGroups": [], "collapse-display": false, "showArrow": false, "textFadeMultiplier": 0, "nodeSizeMultiplier": 0.9960205078125, "lineSizeMultiplier": 1, "collapse-forces": false, "centerStrength": 0.298746744791667, "repelStrength": 13.7809244791667, "linkStrength": 1, "linkDistance": 30, "scale": 1.737146941803905, "close": true}}, "icon": "lucide-git-fork", "title": "配置 CUDA 的一万种方法 的关系图"}}, {"id": "d05b2adb58e8f322", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency", "showSearch": false, "searchQuery": ""}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "dedba0695c22f869", "type": "leaf", "state": {"type": "outline", "state": {"file": "笔记/🤖 AI 人工智能/配置 CUDA 的一万种方法.md"}, "icon": "lucide-list", "title": "配置 CUDA 的一万种方法 的大纲"}}, {"id": "2ac176b8dbb5d7eb", "type": "leaf", "state": {"type": "review-queue-list-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}, {"id": "85e74261f30e425b", "type": "leaf", "state": {"type": "review-queue-list-view", "state": {}, "icon": "lucide-file", "title": "插件不再活动"}}], "currentTab": 4}, {"id": "00f8f8ad2cb9f9b0", "type": "tabs", "dimension": 68.07980049875312, "children": [{"id": "ccb7652bb733a848", "type": "leaf", "state": {"type": "backlink", "state": {"file": "笔记/🤖 AI 人工智能/配置 CUDA 的一万种方法.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "配置 CUDA 的一万种方法 的反向链接列表"}}, {"id": "5f488e06357db2c6", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "笔记/🤖 AI 人工智能/配置 CUDA 的一万种方法.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "配置 CUDA 的一万种方法 的出链列表"}}, {"id": "109c669fb0e4b88f", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true}, "icon": "lucide-tags", "title": "标签"}}], "currentTab": 2}], "direction": "horizontal", "width": 366.5, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "obsidian42-brat:BRAT": false}}, "active": "ea65bc0da501beda", "lastOpenFiles": ["zh-CN/笔记/🧱 基础设施/容器既没有 netstat 和 lsof 也不是 root 时如何排查网络？手动解析 procfs！.md", "zh-CN/笔记/🛠️ 开发/为 DuckDB WASM 制作 Drizzle ORM Driver.md", "zh-CN/笔记/🛠️ 开发/在容器内如何列出正在被监听的端口或者打开的文件？.md", "zh-CN/笔记/📟 终端/软件/容器化/列出 Docker CLI 当前登录的域名.md", "zh-CN/笔记/🛠️ 开发/🗂 数据库/关系型数据库 Relational DB/🐣 DuckDB/各种语句.md", "zh-C<PERSON>/笔记/🤖 AI 人工智能/Neuro/AI VTuber.md", "zh-CN/笔记/🛠️ 开发/🗂 数据库/关系型数据库 Relational DB/🐣 DuckDB", "zh-CN/笔记/🤖 AI 人工智能/Neuro/Archive.zip", "笔记/🧩 开源贡献/Kubernetes/CNCF Traval Funding.md", "笔记/🤖 AI 人工智能/配置 CUDA 的一万种方法.md", "笔记/🛠️ 开发/用 CSS 画一本好看的可以交互的 3D 书.md", "node_modules/@vueuse", "node_modules/@antfu", "vite.config.ts.timestamp-1728465284690-0e28b7c319039.mjs", "vite.config.ts.timestamp-1728464096252-aa8904c364146.mjs", "pnpm-lock.yaml.2653060370", "node_modules/@types", "node_modules/@nolebase", "node_modules/@netlify", "笔记/🛠️ 开发/JS Framework Benchmark 命令速查.md", "笔记/🛠️ 开发/Another Custom Directive API in Vue and Vapor.md", "笔记/🛠️ 开发/因为 dayjs 不能轻松导入全部的 i18n 语言而换用了 date-fns.md", "笔记/🛠️ 开发/用兼容性更好，体积更小的 Rive 渲染和播放 Lottie 动画.md", "笔记/📦 收集箱/🐙 GitHub 项目/maplibre/index.md", "编目 Catalog/文章/理解什么是 CRDT.md", "笔记/🪄 Vtuber/在 macOS 上不同的 VTuber 直播方案.md", "笔记/🪄 Vtuber/assets/Pasted image 20231212122507.png", "笔记/🪄 Vtuber/assets/Pasted image 20231212122502.png", "笔记/🪄 Vtuber/assets/Pasted image 20231209163759.png", "笔记/🪄 Vtuber/assets/Pasted image 20231209163219.png", "笔记/🪄 Vtuber/assets/Pasted image 20231209163146.png", "笔记/🪄 Vtuber/assets/Pasted image 20231209163110.jpg", "笔记/🪄 Vtuber/assets/Pasted image 20231208233938.png", "笔记/🪄 Vtuber/assets/Pasted image 20231208233927.png", "笔记/🪄 Vtuber/assets/Pasted image 20231208233632.png", "笔记/🧱 基础设施/🛜 OpenWRT/assets/Pasted image 20240520003337.png", "笔记/🧱 基础设施/🛜 OpenWRT/从 openclash 迁移到 sing-box.md", "笔记/🧱 基础设施/🚢 Kubernetes/开发一个 Kubernetes Controller.md", "笔记/🧱 基础设施/🚢 Kubernetes/将 TrueNAS 共享存储连接到 K8s 集群.md", "笔记/🧱 基础设施/🚢 Kubernetes/kubectl 速查表.md", "笔记/🧱 基础设施/🚢 Kubernetes/Pod 错误排查之.md", "笔记/🧱 基础设施/云上集群的 GPU.md", "笔记/🧮 数学/空洞的想法.md", "笔记/🧩 开源贡献/Kubernetes/开始之前.md", "笔记/🤖 AI 人工智能/领域/音频/配置和运行 ChatTTS.md", "笔记/🤖 AI 人工智能/领域/音频/在 macOS 上配置 GPT-SoVITS-WebUI.md", "未命名.canvas"]}