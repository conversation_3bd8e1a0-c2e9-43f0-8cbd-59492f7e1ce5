/**
 * VitePress 配置文件
 *
 * 这个文件是 VitePress 静态站点生成器的主配置文件，用于：
 * 1. 配置站点的基本信息（标题、描述等）
 * 2. 配置主题和导航结构
 * 3. 配置 Markdown 渲染器和插件
 * 4. 配置搜索功能
 * 5. 配置多语言支持
 * 6. 配置各种增强功能插件
 */

// 导入 Nólëbase 集成的 Markdown-it 预设配置
import { presetMarkdownIt } from '@nolebase/integrations/vitepress/markdown-it'
// 导入头部元数据转换插件
import { transformHeadMeta } from '@nolebase/vitepress-plugin-meta'
// 导入自动侧边栏生成插件
import { calculateSidebar } from '@nolebase/vitepress-plugin-sidebar'
// 导入 Open Graph 图片生成插件（已注释）
// import { buildEndGenerateOpenGraphImages } from '@nolebase/vitepress-plugin-og-image/vitepress'
// 导入 Markdown 脚注插件
import MarkdownItFootnote from 'markdown-it-footnote'
// 导入 Markdown 数学公式插件
import MarkdownItMathjax3 from 'markdown-it-mathjax3'
// 导入 VitePress 配置定义函数
import { defineConfig } from 'vitepress'

// 导入站点元数据（站点名称、描述、链接等）
import { discordLink, githubRepoLink, siteDescription, siteName } from '../metadata'
// 导入头部配置
import head from './head'

// 初始化 Nólëbase 预设配置
const nolebase = presetMarkdownIt()

// 导出 VitePress 配置
export default defineConfig({
  // Vue 相关配置
  vue: {
    template: {
      // 配置资源 URL 转换，用于处理各种媒体资源的路径
      transformAssetUrls: {
        video: ['src', 'poster'],        // 视频标签的 src 和 poster 属性
        source: ['src'],                 // source 标签的 src 属性
        img: ['src'],                    // 图片标签的 src 属性
        image: ['xlink:href', 'href'],   // SVG image 标签的链接属性
        use: ['xlink:href', 'href'],     // SVG use 标签的链接属性
        NolebaseUnlazyImg: ['src'],      // Nólëbase 懒加载图片组件的 src 属性
      },
    },
  },
  // 站点标题（从 metadata 文件导入）
  title: siteName,
  // 站点描述（从 metadata 文件导入）
  description: siteDescription,
  // 忽略死链接检查，避免构建时因为外部链接失效而报错
  ignoreDeadLinks: true,
  // 头部配置（从 ./head 文件导入）
  head,
  // 主题配置
  themeConfig: {
    // 搜索功能配置
    search: {
      // 使用本地搜索提供者
      provider: 'local',
      options: {
        // 多语言搜索配置
        locales: {
          root: {
            // 搜索界面的中文翻译
            translations: {
              button: {
                buttonText: '搜索文档',
                buttonAriaLabel: '搜索文档',
              },
              modal: {
                noResultsText: '无法找到相关结果',
                resetButtonTitle: '清除查询条件',
                footer: {
                  selectText: '选择',
                  navigateText: '切换',
                },
              },
            },
          },
        },

        // 自定义搜索渲染函数
        // 将标题和标签字段添加到搜索中
        // 可以通过在页面的 frontmatter 中添加 search: false 来排除页面搜索
        _render(src, env, md) {
          // 注意：没有 `md.render(src, env)` 的话，env 中会缺少一些信息
          let html = md.render(src, env)
          let tagsPart = ''      // 标签部分
          let headingPart = ''   // 标题部分
          let contentPart = ''   // 内容部分
          let fullContent = ''   // 完整内容
          // 内容排序函数
          const sortContent = () => [headingPart, tagsPart, contentPart] as const
          let { frontmatter, content } = env

          // 如果没有 frontmatter，直接返回原始 HTML
          if (!frontmatter)
            return html

          // 如果页面明确设置不参与搜索，返回空字符串
          if (frontmatter.search === false)
            return ''

          // 设置内容部分
          contentPart = content ||= src

          // 匹配标题（以 # 开头的行）
          const headingMatch = content.match(/^# .*/m)
          const hasHeading = !!(headingMatch && headingMatch[0] && headingMatch.index !== undefined)

          // 如果有标题，分离标题和内容
          if (hasHeading) {
            const headingEnd = headingMatch.index! + headingMatch[0].length
            headingPart = content.slice(0, headingEnd)
            contentPart = content.slice(headingEnd)
          }
          // 如果没有标题但 frontmatter 中有 title，使用 title 作为标题
          else if (frontmatter.title) {
            headingPart = `# ${frontmatter.title}`
          }

          // 处理标签
          const tags = frontmatter.tags
          if (tags && Array.isArray(tags) && tags.length)
            tagsPart = `Tags: #${tags.join(', #')}`

          // 组合完整内容
          fullContent = sortContent().filter(Boolean).join('\n\n')

          // 重新渲染完整内容
          html = md.render(fullContent, env)

          return html
        },
      },
    },
  },
  // 多语言配置
  locales: {
    // 根语言配置（中文）
    root: {
      lang: 'zh-CN',           // 语言代码
      label: '中文',           // 语言标签
      dir: '/my-note',          // 目录路径
      link: '/my-note',         // 链接路径
      themeConfig: {
        // 导航栏配置
        nav: [
          { text: '测试', link: '/my-note/0-index/' },
          // { text: '主页', link: '/my-note/' },
          // { text: '笔记', link: '/my-note/笔记/' },
          // { text: '编目 Catalog', link: '/my-note/编目 Catalog/' },
          // { text: '最近更新', link: '/my-note/toc' },
        ],
        // 社交链接配置
        socialLinks: [
          { icon: 'github', link: githubRepoLink },
          { icon: 'discord', link: discordLink },
        ],
        // 深色模式切换按钮标签
        darkModeSwitchLabel: '切换主题',
        // 页面大纲配置
        outline: { label: '页面大纲', level: 'deep' },
        // 编辑链接配置
        editLink: {
          pattern: `${githubRepoLink}/tree/main/:path`,
          text: '编辑本页面',
        },
        // 自动生成侧边栏
        sidebar: calculateSidebar([
          { folderName: 'my-note/0-Inbox', separate: true },
          // { folderName: 'my-note/笔记', separate: true },
          // { folderName: 'my-note/编目 Catalog', separate: true },
        ], 'my-note'),
        // 页脚配置
        footer: {
          message: '用 <span style="color: #e25555;">&#9829;</span> 撰写',
          copyright:
        '<a class="footer-cc-link" target="_blank" href="https://creativecommons.org/licenses/by-sa/4.0/">CC BY-SA 4.0</a> © 2022-PRESENT Nólëbase 的创作者们',
        },
      },
    },
  },
  // Markdown 配置
  markdown: {
    // 代码高亮主题配置
    theme: {
      light: 'github-light',    // 浅色主题
      dark: 'one-dark-pro',     // 深色主题
    },
    // 启用数学公式支持
    math: true,
    // Markdown 预配置（异步）
    preConfig: async (md) => {
      // 安装 Nólëbase 预设配置
      await nolebase.install(md)
    },
    // Markdown 配置
    config: (md) => {
      // 使用脚注插件
      md.use(MarkdownItFootnote)
      // 使用数学公式插件
      md.use(MarkdownItMathjax3)
    },
  },
  // 头部元数据转换函数
  async transformHead(context) {
    let head = [...context.head]

    // 使用 Nólëbase 的头部元数据转换
    const returnedHead = await transformHeadMeta()(head, context)
    if (typeof returnedHead !== 'undefined')
      head = returnedHead

    return head
  },
  // 构建结束时的钩子函数（已注释）
  // 用于生成 Open Graph 图片
  // async buildEnd(siteConfig) {
  //   await buildEndGenerateOpenGraphImages({
  //     baseUrl: targetDomain,
  //     category: {
  //       byLevel: 2,
  //     },
  //   })(siteConfig)
  // },
})
