/**
 * Colors: Palette
 *
 * The primitive colors used for accent colors. These colors are referenced
 * by functional colors such as "Text", "Background", or "Brand".
 *
 * Each colors have exact same color scale system with 3 levels of solid
 * colors with different brightness, and 1 soft color.
 *
 * - `XXX-1`: The most solid color used mainly for colored text. It must
 *   satisfy the contrast ratio against when used on top of `XXX-soft`.
 *
 * - `XXX-2`: The color used mainly for hover state of the button.
 *
 * - `XXX-3`: The color for solid background, such as bg color of the button.
 *    It must satisfy the contrast ratio with pure white (#ffffff) text on
 *    top of it.
 *
 * - `XXX-soft`: The color used for subtle background such as custom container
 *    or badges. It must satisfy the contrast ratio when putting `XXX-1` colors
 *    on top of it.
 *
 *    The soft color must be semi transparent alpha channel. This is crucial
 *    because it allows adding multiple "soft" colors on top of each other
 *    to create a accent, such as when having inline code block inside
 *    custom containers.
 * -------------------------------------------------------------------------- */

:root {
  --vp-c-sky-1: #3c96ca;
  --vp-c-sky-2: #49a1d4;
  --vp-c-sky-3: #49a1d4;
  --vp-c-sky-soft: rgba(73, 161, 212, 0.84);
}

.dark {
  --vp-c-sky-1: #49a3d7;
  --vp-c-sky-2: #58b1e5;
  --vp-c-sky-3: #58b1e5;
  --vp-c-sky-soft: #58b1e5;
}

/**
 * Colors: Function
 *
 * - `default`: The color used purely for subtle indication without any
 *   special meanings attched to it such as bg color for menu hover state.
 *
 * - `brand`: Used for primary brand colors, such as link text, button with
 *   brand theme, etc.
 *
 * - `tip`: Used to indicate useful information. The default theme uses the
 *   brand color for this by default.
 *
 * - `warning`: Used to indicate warning to the users. Used in custom
 *   container, badges, etc.
 *
 * - `danger`: Used to show error, or dangerous message to the users. Used
 *   in custom container, badges, etc.
 *
 * To understand the scaling system, refer to "Colors: Palette" section.
 * -------------------------------------------------------------------------- */

:root {
  /* 主色 */
  --vp-c-brand-1: var(--vp-c-sky-1);
  --vp-c-brand-2: var(--vp-c-sky-2);
  --vp-c-brand-3: var(--vp-c-sky-3);
  --vp-c-brand-soft: var(--vp-c-sky-soft);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(
    120deg,
    #8d6fc7 40%,
    #4fc4d8
  );
  --vp-home-hero-image-background-image: linear-gradient(
    -45deg,
    #8d73bf90 30%,
    #d6c0e890
  );
  --vp-home-hero-image-filter: blur(30px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(72px);
  }
}

/**
 * Component: Badge
 * -------------------------------------------------------------------------- */
:root {
  /* 配置一下 tips 的自定义块的颜色为非默认的产品色，避免与文字不兼容导致可读性下降 */
  --vp-custom-block-tip-bg: #def4f4;
  --vp-custom-block-tip-code-bg: #cbd9dd7d;
}

.dark:root {
  /* 配置一下在暗色模式下 tips 的自定义块的颜色为非默认的产品色，避免与文字不兼容导致可读性下降 */
  --vp-custom-block-tip-bg: #02474e;
  --vp-custom-block-tip-code-bg: #0d1e1e9c;
}

:root {
  --vp-nolebase-highlight-targeted-heading-bg: rgba(253, 216, 95, 0.31);
}
