{
  "cSpell.words": [
    "antfu",
    "Attributify",
    "giscus",
    "Gitcus",
    "headlessui",
    "hedgedoc",
    "iconify",
    "mambaforge",
    "mathjax",
    "miniforge",
    "nolebase",
    "octicon",
    "pipenv",
    "pixi",
    "unlazy",
    "unocss",
    "unplugin",
    "vitepress",
    "vuedraggable",
    "vueuse",
    "xlink"
  ],
  "prettier.enable": false,
  "editor.formatOnSave": false,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.organizeImports": "never"
  },
  // Enable the ESlint flat config support
  "eslint.experimental.useFlatConfig": true,
  // The following is optional.
  // It's better to put under project setting `.vscode/settings.json`
  // to avoid conflicts with working with different eslint configs
  // that does not support all formats.
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "yaml"
  ],
  "unocss.root": [
    "."
  ],
  "typescript.tsdk": "node_modules/typescript/lib"
}
