## 核心功能

- 验证（Validate）
  - 作用：在资源创建/更新时，检查资源是否符合预定义的策略，违规则阻止
  - 场景：
    - 禁止创建`Pod`使用`hostNetwork`
- 修改（Mutate）
  - 作用：在资源创建/更新时，自动补全或补全字段
  - 场景：
    - 自动为`Pod`设置默认的CPU/内存限制
- 生成（Generate）
  - 作用：在检测到特定条件时，自动创建或同步其他 Kubernetes 资源
  - 场景：
    - 新命名空间自动生成 `NetworkPolicy`

## 玩转Kyverno

> k8s版本：v1.29.9
>
> kyverno版本：v3.3.8

### 1. 安装Kyverno

```
helm repo add kyverno https://kyverno.github.io/kyverno/
helm repo update
helm search repo kyverno/kyverno --versions

helm install kyverno kyverno/kyverno \
  --namespace kyverno --create-namespace \
  --version v3.3.8
```



### 创建策略示例

#### 策略 1：禁止 Pod 使用 `hostNetwork`

```
# hostnetwork-block.yaml
apiVersion: kyverno.io/v1
kind: ClusterPolicy
metadata:
  name: block-hostnetwork
spec:
  validationFailureAction: Enforce
  background: true
  rules:
  - name: hostnetwork-not-allowed
    match:
      any:
      - resources:
          kinds:
          - Pod
    validate:
      message: "HostNetwork is not allowed as it poses security risks."
      pattern:
        spec:
          hostNetwork: "false"
```

#### 策略 2：自动为 Pod 设置默认资源限制

```
# set-default-resources.yaml
apiVersion: kyverno.io/v1
kind: ClusterPolicy
metadata:
  name: set-default-resources
spec:
  background: true
  rules:
  - name: inject-default-resources
    match:
      any:
      - resources:
          kinds:
          - Pod
    mutate:
      patchStrategicMerge:
        spec:
          containers:
          - (name): "*"
            resources:
              limits:
                cpu: "500m"
                memory: "512Mi"
              requests:
                cpu: "100m"
                memory: "128Mi"
```



### 3. 应用策略

```
kubectl apply -f hostnetwork-block.yaml
kubectl apply -f set-default-resources.yaml
```



### 4. 验证策略

#### 测试 `hostNetwork` 阻止策略

```
# 尝试创建一个使用 hostNetwork 的 Pod
kubectl run test-hostnetwork --image=nginx --overrides='{"spec":{"hostNetwork":true}}'

# 预期输出：
# Error from server: admission webhook "validate.kyverno.svc" denied the request...
```

#### 测试自动资源限制

```
# 创建一个普通 Pod
kubectl run test-resources --image=nginx

# 检查资源限制
kubectl get pod test-resources -o jsonpath='{.spec.containers[0].resources}'

# 预期输出：
# {"limits":{"cpu":"500m","memory":"512Mi"},"requests":{"cpu":"100m","memory":"128Mi"}}
```

## Kyverno和自研Webhook如何选择？

1. **选Kyverno：**
   - 场景：Kubernetes 原生策略（校验、突变、生成）
   - 优势：社区有大量维护的 [策略库](https://kyverno.io/policies/)、维护成本低
   - 典型案例：禁止`hostNetwork`
2. **选自研Webhook：**
   - 场景：非标准化场景（如调用外部系统）、复杂业务逻辑
   - 优势：完全可控、自由度高
   - 典型案例：外部审批流程、自定义资源校验

> Kyverno短板应对：超复杂逻辑用`External Data`扩展或结合OPA





