{"name": "nolebase", "type": "module", "version": "1.0.0", "packageManager": "pnpm@10.14.0", "author": {"name": "Nólëbase", "email": "<EMAIL>", "url": "https://github.com/nolebase"}, "license": "MIT", "keywords": ["vitepress", "nolebase", "markdown", "nolebase-integration", "obsidian", "knowledge-base", "vitepress-doc"], "scripts": {"dev": "pnpm run docs:dev", "build": "pnpm run docs:build", "serve": "pnpm run docs:serve", "docs:dev": "vitepress dev", "docs:build": "vitepress build", "docs:serve": "vitepress serve", "update": "tsx scripts/update.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "devDependencies": {"@antfu/eslint-config": "^4.19.0", "@hedgedoc/markdown-it-plugins": "^2.1.4", "@iconify-json/eos-icons": "^1.2.2", "@iconify-json/ic": "^1.2.2", "@iconify-json/octicon": "^1.2.9", "@iconify-json/svg-spinners": "^1.2.2", "@netlify/functions": "^4.2.1", "@nolebase/integrations": "2.18.1", "@nolebase/markdown-it-bi-directional-links": "^2.18.1", "@nolebase/markdown-it-unlazy-img": "^2.18.1", "@nolebase/vitepress-plugin-enhanced-mark": "^2.18.1", "@nolebase/vitepress-plugin-enhanced-readabilities": "^2.18.1", "@nolebase/vitepress-plugin-git-changelog": "^2.18.1", "@nolebase/vitepress-plugin-highlight-targeted-heading": "^2.18.1", "@nolebase/vitepress-plugin-index": "^2.18.1", "@nolebase/vitepress-plugin-inline-link-preview": "^2.18.1", "@nolebase/vitepress-plugin-meta": "^2.18.1", "@nolebase/vitepress-plugin-og-image": "^2.18.1", "@nolebase/vitepress-plugin-page-properties": "^2.18.1", "@nolebase/vitepress-plugin-sidebar": "^2.18.1", "@nolebase/vitepress-plugin-thumbnail-hash": "^2.18.1", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.20", "@types/markdown-it": "^14.1.2", "@types/markdown-it-footnote": "^3.0.4", "@types/node-fetch": "^2.6.13", "@unocss/eslint-config": "^66.3.3", "@unocss/reset": "^66.3.3", "@vueuse/core": "^13.6.0", "@vueuse/shared": "^13.6.0", "emoji-regex": "^10.4.0", "eslint": "^9.32.0", "fast-glob": "^3.3.3", "fs-extra": "^11.3.0", "gray-matter": "^4.0.3", "less": "^4.4.0", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-footnote": "^4.0.0", "markdown-it-mathjax3": "^4.3.2", "node-fetch": "^3.3.2", "sharp": "^0.34.3", "simple-git": "^3.28.0", "tsx": "^4.20.3", "typescript": "^5.9.2", "unocss": "^66.3.3", "unplugin-vue-components": "^28.8.0", "uuid": "^11.1.0", "vite": "^7.0.6", "vite-plugin-inspect": "^11.3.2", "vitepress": "^2.0.0-alpha.9", "vue": "^3.5.18"}}