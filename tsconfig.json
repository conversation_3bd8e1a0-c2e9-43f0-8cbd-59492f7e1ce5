{"compilerOptions": {"target": "ESNext", "jsx": "preserve", "lib": ["DOM", "ESNext"], "baseUrl": ".", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "types": ["vite/client"], "allowJs": true, "strict": true, "strictNullChecks": true, "noUnusedLocals": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["**/*.ts", "**/*.d.ts", "**/*.tsx", "**/*.vue", ".vitepress/**/*.ts", ".vitepress/**/*.tsx", ".vitepress/**/*.vue"]}