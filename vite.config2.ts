/**
 * Vite 配置文件
 *
 * 这个文件是 Vite 构建工具的配置文件，用于：
 * 1. 配置构建优化选项
 * 2. 配置 Vite 插件（组件自动导入、CSS 框架、开发工具等）
 * 3. 配置资源处理规则
 * 4. 配置 Nólëbase 集成功能（Git 变更日志、页面属性等）
 *
 * 与 VitePress 配置文件的区别：
 * - VitePress 配置主要关注站点结构、主题、内容渲染
 * - Vite 配置主要关注构建过程、开发体验、性能优化
 */

// 导入 Node.js 路径处理模块
import { join } from 'node:path'
// 导入 Nólëbase 的 Vite 预设配置
import { presetVite } from '@nolebase/integrations/vitepress/vite'
// 导入 UnoCSS 原子化 CSS 框架
import UnoCSS from 'unocss/vite'

// 导入 Vue 组件自动导入插件
import Components from 'unplugin-vue-components/vite'
// 导入 Vite 配置定义函数
import { defineConfig } from 'vite'
// 导入 Vite 开发调试插件
import Inspect from 'vite-plugin-inspect'

// 导入项目元数据（创作者信息、仓库链接等）
import { creators, githubRepoLink } from './metadata'

// 导出 Vite 配置（异步函数）
export default defineConfig(async () => {
  // 配置 Nólëbase 预设
  const nolebase = presetVite({
    // Git 变更日志配置
    gitChangelog: {
      options: {
        gitChangelog: {
          // 仓库 URL 配置
          repoURL: () => githubRepoLink,
          // 作者映射配置
          mapAuthors: <AUTHORS>
        },
        markdownSection: {
          // 排除不需要显示变更日志的文件
          excludes: [
            join('zh-CN', 'toc.md'),    // 目录页面
            join('zh-CN', 'index.md'),  // 首页
          ],
        },
      },
    },
    // 页面属性配置
    pageProperties: {
      options: {
        markdownSection: {
          // 排除不需要显示页面属性的文件
          excludes: [
            join('zh-CN', 'toc.md'),    // 目录页面
            join('zh-CN', 'index.md'),  // 首页
          ],
        },
      },
    },
  })

  // 返回 Vite 配置对象
  return {
    // 资源包含配置
    assetsInclude: [
      '**/*.mov',  // 包含 .mov 视频文件作为静态资源
    ],
    // 依赖优化配置
    optimizeDeps: {
      // VitePress 使用别名替换 `join(DIST_CLIENT_PATH, '/index')`
      // 需要从优化中排除以避免构建问题
      exclude: [
        'vitepress',
      ],
    },
    // 插件配置
    plugins: [
      // Vite 开发调试插件，提供构建分析界面
      Inspect(),
      // Vue 组件自动导入插件
      Components({
        include: [/\.vue$/, /\.md$/],              // 处理 .vue 和 .md 文件
        dirs: '.vitepress/theme/components',       // 组件目录
        dts: '.vitepress/components.d.ts',         // TypeScript 声明文件输出路径
      }),
      // UnoCSS 原子化 CSS 框架
      UnoCSS(),
      // Nólëbase 预设插件
      nolebase,
      // 展开 Nólëbase 的所有插件
      ...nolebase.plugins(),
    ],
  }
})
