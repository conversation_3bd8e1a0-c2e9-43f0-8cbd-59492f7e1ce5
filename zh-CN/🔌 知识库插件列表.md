# 知识库插件列表

## 必选

**⚠️ 注意：这些插件在知识库中参与了文档格式化、文件解析、Markdown 语法增强、拓展等功能，如果不安装可能会导致无法正常渲染和阅读**

### Admonition

说明：将警告块样式的内容添加到 Obsidian，样式遵循 [Material for MkDocs](https://squidfunk.github.io/mkdocs-material/reference/admonitions/)
类型：Markdown 语法拓展
地址：[valentine195/obsidian-admonition: Adds admonition block-styled content to Obsidian.md](https://github.com/valentine195/obsidian-admonition)

### Asciinema Player

说明：它支持将 asciicast 文件嵌入到您的 Markdown 文件中的插件，在预览页面就可以看到录制的命令行记录
类型：Markdown 语法拓展，文件解析
地址：[nekomeowww/obsidian-asciinema-player: This is a Obsidian plugin which supports embedding asciicast files into Markdown files](https://github.com/nekomeowww/obsidian-asciinema-player)

### Obsidian Footnotes Plugin

说明：支持使用快捷键快速添加脚注，脚注将会自动递增，使编辑和引用外部来源可以更简单，使用 <kbd data-macos-keyboard-key="command">command</kbd>+<kbd>Shift</kbd>+<kbd>6</kbd> 快速创建脚注
类型：Markdown 语法增强
地址：[akaalias/obsidian-footnotes: Makes creating footnotes in Obsidian more fun!](https://github.com/akaalias/obsidian-footnotes)

### Dataview

说明：支持在 Markdown 文件中使用类似 SQL 的语法查询数据然后可视化结果
类型：嵌入块增强，Markdown 语法拓展，数据查询
地址：[blacksmithgu/obsidian-dataview: A high-performance data index and query language over Markdown files, for https://obsidian.md/.](https://github.com/blacksmithgu/obsidian-dataview)

## 可选

**以下插件不安装并不会影响整体使用体验**

### Obsidian Git

说明：支持在安装有 Git 命令的设备和环境上通过命令面板自动创建和拉取提交
类型：功能拓展
地址：[denolehov/obsidian-git: Backup your Obsidian.md vault with git](https://github.com/denolehov/obsidian-git)

### Auto Pair Chinese Symbol

说明：支持 `《》 【】（）‘’ “” 「」`  符号输入时自动补齐
类型：功能拓展，Markdown 语法增强
地址：[renmu123/obsidian-auto-pair-chinese-symbol: 中文符号自动补齐](https://github.com/renmu123/obsidian-auto-pair-chinese-symbol)

### File Explorer Note Count

说明：支持在文件浏览标签页中展示包含的文档数
类型：功能拓展
地址：[ozntel/file-explorer-note-count: Obsidian Plugin for viewing the number of elements under each folder within the file explorer](https://github.com/ozntel/file-explorer-note-count)

### Remember Cursor Position

说明：插件会记住每个文档的光标和滚动位置。这使得在笔记、链接之间切换、返回时非常方便，从而无需和找到滚动到上次所在的位置
类型：功能拓展
地址：[dy-sh/obsidian-remember-cursor-position: Obsidian plugin. Remember cursor position for each note](https://github.com/dy-sh/obsidian-remember-cursor-position)

### Advanced Tables

说明：用于格式化、方便编辑和导航表格的插件
类型：功能拓展，Markdown 语法增强，表格
地址：[tgrosinger/advanced-tables-obsidian: Improved table navigation, formatting, and manipulation in Obsidian.md](https://github.com/tgrosinger/advanced-tables-obsidian)
